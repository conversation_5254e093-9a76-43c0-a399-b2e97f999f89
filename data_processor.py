"""
Data Processing Module for Core Values Visualization

This module processes team member and manager ratings for core values
and prepares data for visualization in the web interface.
"""

import json
import statistics
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Tuple


@dataclass
class TeamMemberRating:
    """Represents a team member's rating for core values"""

    user_id: str
    name: str
    role: str
    ratings: Dict[str, int]  # core_value -> rating (1-5)
    timestamp: datetime

    def __post_init__(self):
        """Validate ratings are within 1-5 range"""
        for value, rating in self.ratings.items():
            if not 1 <= rating <= 5:
                raise ValueError(
                    f"Rating for {value} must be between 1 and 5, got {rating}"
                )


@dataclass
class ManagerRating:
    """Represents a manager's rating for core values"""

    user_id: str
    name: str
    ratings: Dict[str, int]  # core_value -> rating (1-5)
    timestamp: datetime

    def __post_init__(self):
        """Validate ratings are within 1-5 range"""
        for value, rating in self.ratings.items():
            if not 1 <= rating <= 5:
                raise ValueError(
                    f"Rating for {value} must be between 1 and 5, got {rating}"
                )


class CoreValuesDataProcessor:
    """Processes core values data for visualization"""

    def __init__(self, core_values: List[str] = None):
        """
        Initialize with core values list

        Args:
            core_values: List of core values to track
        """
        self.core_values = core_values or [
            "Důvěra",
            "Odpovědnost",
            "Inovace",
            "Spolupráce",
            "Respekt",
        ]
        self.team_ratings: List[TeamMemberRating] = []
        self.manager_rating: ManagerRating = None

    def add_team_member_rating(
        self,
        user_id: str,
        name: str,
        role: str,
        ratings: Dict[str, int],
        timestamp: datetime = None,
    ) -> None:
        """
        Add a team member's rating

        Args:
            user_id: Unique identifier for the user
            name: Display name of the team member
            role: Role/position of the team member
            ratings: Dictionary mapping core values to ratings (1-5)
            timestamp: When the rating was submitted (defaults to now)
        """
        if timestamp is None:
            timestamp = datetime.now()

        # Validate all core values are rated
        missing_values = set(self.core_values) - set(ratings.keys())
        if missing_values:
            raise ValueError(f"Missing ratings for core values: {missing_values}")

        rating = TeamMemberRating(user_id, name, role, ratings, timestamp)
        self.team_ratings.append(rating)

    def set_manager_rating(
        self,
        user_id: str,
        name: str,
        ratings: Dict[str, int],
        timestamp: datetime = None,
    ) -> None:
        """
        Set the manager's rating

        Args:
            user_id: Unique identifier for the manager
            name: Display name of the manager
            ratings: Dictionary mapping core values to ratings (1-5)
            timestamp: When the rating was submitted (defaults to now)
        """
        if timestamp is None:
            timestamp = datetime.now()

        # Validate all core values are rated
        missing_values = set(self.core_values) - set(ratings.keys())
        if missing_values:
            raise ValueError(f"Missing ratings for core values: {missing_values}")

        self.manager_rating = ManagerRating(user_id, name, ratings, timestamp)

    def calculate_team_averages(self) -> Dict[str, float]:
        """
        Calculate average ratings for each core value across team members

        Returns:
            Dictionary mapping core values to average ratings
        """
        if not self.team_ratings:
            return {value: 0.0 for value in self.core_values}

        averages = {}
        for value in self.core_values:
            ratings = [member.ratings[value] for member in self.team_ratings]
            averages[value] = round(statistics.mean(ratings), 1)

        return averages

    def calculate_team_statistics(self) -> Dict[str, Dict[str, float]]:
        """
        Calculate comprehensive statistics for team ratings

        Returns:
            Dictionary with statistics for each core value
        """
        if not self.team_ratings:
            return {}

        stats = {}
        for value in self.core_values:
            ratings = [member.ratings[value] for member in self.team_ratings]
            stats[value] = {
                "mean": round(statistics.mean(ratings), 2),
                "median": statistics.median(ratings),
                "stdev": round(statistics.stdev(ratings) if len(ratings) > 1 else 0, 2),
                "min": min(ratings),
                "max": max(ratings),
                "range": max(ratings) - min(ratings),
            }

        return stats

    def get_manager_vs_team_comparison(self) -> Dict[str, Dict[str, float]]:
        """
        Compare manager ratings with team averages

        Returns:
            Dictionary with comparison data for each core value
        """
        if not self.manager_rating or not self.team_ratings:
            return {}

        team_averages = self.calculate_team_averages()
        comparison = {}

        for value in self.core_values:
            manager_rating = self.manager_rating.ratings[value]
            team_avg = team_averages[value]

            comparison[value] = {
                "manager_rating": manager_rating,
                "team_average": team_avg,
                "difference": round(manager_rating - team_avg, 1),
                "percentage_diff": (
                    round(((manager_rating - team_avg) / team_avg) * 100, 1)
                    if team_avg > 0
                    else 0
                ),
            }

        return comparison

    def export_for_visualization(self) -> Dict[str, Any]:
        """
        Export data in format suitable for web visualization

        Returns:
            Dictionary with all data formatted for Chart.js radar chart
        """
        # Prepare team member data
        team_data = []
        team_names = []

        for member in self.team_ratings:
            member_ratings = [member.ratings[value] for value in self.core_values]
            team_data.append(member_ratings)
            team_names.append(member.name)

        # Prepare manager data
        manager_data = []
        manager_name = ""
        if self.manager_rating:
            manager_data = [
                self.manager_rating.ratings[value] for value in self.core_values
            ]
            manager_name = self.manager_rating.name

        # Calculate team averages
        team_averages = self.calculate_team_averages()
        team_avg_data = [team_averages[value] for value in self.core_values]

        return {
            "core_values": self.core_values,
            "team_members": team_names,
            "team_data": team_data,
            "team_averages": team_avg_data,
            "manager_name": manager_name,
            "manager_data": manager_data,
            "statistics": self.calculate_team_statistics(),
            "comparison": self.get_manager_vs_team_comparison(),
            "timestamp": datetime.now().isoformat(),
        }

    def load_from_database_records(self, records: List[Dict[str, Any]]) -> None:
        """
        Load data from database records

        Args:
            records: List of database records with user ratings
                    Expected format: {
                        'user_id': str,
                        'name': str,
                        'role': str,  # 'team_member' or 'manager'
                        'core_value': str,
                        'rating': int,
                        'timestamp': datetime or str
                    }
        """
        # Group records by user
        user_data = {}
        for record in records:
            user_id = record["user_id"]
            if user_id not in user_data:
                user_data[user_id] = {
                    "name": record["name"],
                    "role": record["role"],
                    "ratings": {},
                    "timestamp": record.get("timestamp", datetime.now()),
                }

            user_data[user_id]["ratings"][record["core_value"]] = record["rating"]

        # Process each user's data
        for user_id, data in user_data.items():
            timestamp = data["timestamp"]
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp)

            if data["role"] == "manager":
                self.set_manager_rating(
                    user_id, data["name"], data["ratings"], timestamp
                )
            else:
                self.add_team_member_rating(
                    user_id, data["name"], data["role"], data["ratings"], timestamp
                )

    def export_to_json(self, filepath: str = None) -> str:
        """
        Export visualization data to JSON

        Args:
            filepath: Optional file path to save JSON

        Returns:
            JSON string of the visualization data
        """
        data = self.export_for_visualization()
        json_str = json.dumps(data, indent=2, ensure_ascii=False)

        if filepath:
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(json_str)

        return json_str

    def save_dashboard_data(self, output_dir: str = "dashboard_data") -> Dict[str, str]:
        """
        Save multiple JSON files for dashboard consumption

        Args:
            output_dir: Directory to save JSON files

        Returns:
            Dictionary with file paths of created JSON files
        """
        import os

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Export main visualization data
        viz_data = self.export_for_visualization()
        viz_file = os.path.join(output_dir, "visualization_data.json")
        with open(viz_file, "w", encoding="utf-8") as f:
            json.dump(viz_data, f, indent=2, ensure_ascii=False)

        # Export team statistics
        stats_data = {
            "statistics": self.calculate_team_statistics(),
            "team_averages": self.calculate_team_averages(),
            "timestamp": datetime.now().isoformat(),
        }
        stats_file = os.path.join(output_dir, "team_statistics.json")
        with open(stats_file, "w", encoding="utf-8") as f:
            json.dump(stats_data, f, indent=2, ensure_ascii=False)

        # Export comparison data
        comparison_data = {
            "comparison": self.get_manager_vs_team_comparison(),
            "timestamp": datetime.now().isoformat(),
        }
        comparison_file = os.path.join(output_dir, "manager_comparison.json")
        with open(comparison_file, "w", encoding="utf-8") as f:
            json.dump(comparison_data, f, indent=2, ensure_ascii=False)

        # Export raw data for further analysis
        raw_data = {
            "core_values": self.core_values,
            "team_ratings": [
                {
                    "user_id": member.user_id,
                    "name": member.name,
                    "role": member.role,
                    "ratings": member.ratings,
                    "timestamp": member.timestamp.isoformat(),
                }
                for member in self.team_ratings
            ],
            "manager_rating": (
                {
                    "user_id": self.manager_rating.user_id,
                    "name": self.manager_rating.name,
                    "ratings": self.manager_rating.ratings,
                    "timestamp": self.manager_rating.timestamp.isoformat(),
                }
                if self.manager_rating
                else None
            ),
            "export_timestamp": datetime.now().isoformat(),
        }
        raw_file = os.path.join(output_dir, "raw_data.json")
        with open(raw_file, "w", encoding="utf-8") as f:
            json.dump(raw_data, f, indent=2, ensure_ascii=False)

        return {
            "visualization_data": viz_file,
            "team_statistics": stats_file,
            "manager_comparison": comparison_file,
            "raw_data": raw_file,
        }


# Example usage and test data
def create_sample_data() -> CoreValuesDataProcessor:
    """Create sample data for testing"""
    processor = CoreValuesDataProcessor()

    # Add team members
    processor.add_team_member_rating(
        "user1",
        "Alice",
        "Developer",
        {"Důvěra": 4, "Odpovědnost": 3, "Inovace": 5, "Spolupráce": 2, "Respekt": 4},
    )
    processor.add_team_member_rating(
        "user2",
        "Bob",
        "Designer",
        {"Důvěra": 3, "Odpovědnost": 4, "Inovace": 4, "Spolupráce": 3, "Respekt": 5},
    )
    processor.add_team_member_rating(
        "user3",
        "Charlie",
        "Analyst",
        {"Důvěra": 2, "Odpovědnost": 3, "Inovace": 3, "Spolupráce": 4, "Respekt": 3},
    )
    processor.add_team_member_rating(
        "user4",
        "Dana",
        "QA Engineer",
        {"Důvěra": 5, "Odpovědnost": 4, "Inovace": 4, "Spolupráce": 4, "Respekt": 5},
    )
    processor.add_team_member_rating(
        "user5",
        "Eli",
        "DevOps",
        {"Důvěra": 3, "Odpovědnost": 3, "Inovace": 4, "Spolupráce": 3, "Respekt": 4},
    )

    # Add manager
    processor.set_manager_rating(
        "mgr1",
        "Manager",
        {"Důvěra": 4, "Odpovědnost": 5, "Inovace": 4, "Spolupráce": 5, "Respekt": 5},
    )

    return processor


if __name__ == "__main__":
    # Example usage
    processor = create_sample_data()

    # Export for visualization
    viz_data = processor.export_for_visualization()
    print("Visualization Data:")
    print(json.dumps(viz_data, indent=2, ensure_ascii=False))

    # Show statistics
    print("\nTeam Statistics:")
    stats = processor.calculate_team_statistics()
    for core_value, stat in stats.items():
        print(f"{core_value}: Mean={stat['mean']}, StdDev={stat['stdev']}")

    # Show comparison
    print("\nManager vs Team Comparison:")
    comparison = processor.get_manager_vs_team_comparison()
    for core_value, comp in comparison.items():
        print(
            f"{core_value}: Manager={comp['manager_rating']}, Team={comp['team_average']}, Diff={comp['difference']}"
        )

    # Save dashboard data
    print("\nSaving dashboard data...")
    files = processor.save_dashboard_data()
    print("Created files:")
    for file_type, filepath in files.items():
        print(f"  {file_type}: {filepath}")
