#!/usr/bin/env python3
"""
Script to generate JSON files for dashboard consumption
"""

from data_processor import CoreValuesDataProcessor, create_sample_data
import os
import json

def main():
    """Generate dashboard data files"""
    print("🚀 Generating dashboard data...")
    
    # Create sample data
    processor = create_sample_data()
    
    # Save dashboard data
    files = processor.save_dashboard_data("dashboard_data")
    
    print("\n✅ Successfully created dashboard files:")
    for file_type, filepath in files.items():
        file_size = os.path.getsize(filepath)
        print(f"  📄 {file_type}: {filepath} ({file_size} bytes)")
    
    # Show sample content from each file
    print("\n📊 Sample content from each file:")
    
    # Visualization data
    with open(files['visualization_data'], 'r', encoding='utf-8') as f:
        viz_data = json.load(f)
    print(f"\n🎯 Visualization Data:")
    print(f"  - Core values: {viz_data['core_values']}")
    print(f"  - Team members: {viz_data['team_members']}")
    print(f"  - Manager: {viz_data['manager_name']}")
    
    # Team statistics
    with open(files['team_statistics'], 'r', encoding='utf-8') as f:
        stats_data = json.load(f)
    print(f"\n📈 Team Statistics:")
    for value, stats in stats_data['statistics'].items():
        print(f"  - {value}: Mean={stats['mean']}, StdDev={stats['stdev']}")
    
    # Manager comparison
    with open(files['manager_comparison'], 'r', encoding='utf-8') as f:
        comp_data = json.load(f)
    print(f"\n⚖️  Manager vs Team Comparison:")
    for value, comp in comp_data['comparison'].items():
        diff = comp['difference']
        symbol = "+" if diff > 0 else ""
        print(f"  - {value}: {symbol}{diff} difference")
    
    print(f"\n🎉 Dashboard data ready! Files saved in 'dashboard_data' directory.")
    print(f"📁 You can now use these JSON files in your dashboard application.")

if __name__ == "__main__":
    main()
