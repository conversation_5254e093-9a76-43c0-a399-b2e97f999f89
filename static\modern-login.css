/* Modern Login Design with Pantone Colors */
:root {
  /* Pantone-inspired Color Palette */
  --pantone-blue: #0F4C75;        /* Classic Blue */
  --pantone-coral: #FF6B6B;       /* Living Coral */
  --pantone-mint: #4ECDC4;        /* Mint */
  --pantone-lavender: #A8E6CF;    /* Lavender */
  --pantone-yellow: #FFD93D;      /* Illuminating Yellow */
  --pantone-purple: #6C5CE7;      /* Purple */
  --pantone-orange: #FD79A8;      /* Orange */

  /* Main Color System */
  --primary-color: var(--pantone-coral);
  --secondary-color: var(--pantone-blue);
  --accent-color: var(--pantone-mint);
  --highlight-color: var(--pantone-yellow);
  --text-primary: #2D3436;
  --text-secondary: #636E72;
  --text-light: #B2BEC3;
  --background-primary: #FFFFFF;
  --background-secondary: #F8F9FA;
  --background-dark: #2D3436;

  /* Modern Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  /* Spacing System */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px rgba(0, 0, 0, 0.15);

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
}

.modern-login-body {
  font-family: var(--font-primary);
  font-weight: var(--font-weight-regular);
  line-height: 1.6;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--background-secondary) 0%, var(--background-primary) 100%);
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

/* Background Elements */
.login-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  overflow: hidden;
}

.bg-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 107, 107, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 107, 107, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

.bg-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.shape {
  position: absolute;
  border-radius: var(--radius-2xl);
  opacity: 0.1;
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, var(--pantone-coral), var(--pantone-orange));
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, var(--pantone-mint), var(--pantone-lavender));
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 180px;
  height: 180px;
  background: linear-gradient(135deg, var(--pantone-purple), var(--pantone-blue));
  bottom: 20%;
  left: 15%;
  animation-delay: 4s;
}

.shape-4 {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, var(--pantone-yellow), var(--pantone-orange));
  bottom: 15%;
  right: 20%;
  animation-delay: 6s;
}

/* Login Wrapper */
.login-wrapper {
  position: relative;
  z-index: 10;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-xl);
}

/* Login Card */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: var(--radius-2xl);
  padding: var(--space-3xl);
  width: 100%;
  max-width: 480px;
  box-shadow: var(--shadow-2xl);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--accent-color), var(--highlight-color));
}

/* Header */
.login-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
}

.brand-logo {
  margin-bottom: var(--space-lg);
}

.brand-text {
  font-size: 2rem;
  font-weight: var(--font-weight-extrabold);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-title {
  font-size: 1.75rem;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.login-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
}

/* Error Message */
.error-message {
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.2);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-xl);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  animation: errorSlideIn 0.5s ease-out;
}

.error-icon {
  color: var(--primary-color);
  font-size: 1.25rem;
}

.error-message p {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

/* Form Styles */
.modern-login-form {
  margin-bottom: var(--space-xl);
}

.input-group {
  margin-bottom: var(--space-xl);
}

.input-label {
  display: block;
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  font-size: 0.95rem;
}

/* Email Input */
.input-wrapper {
  position: relative;
  transition: var(--transition-normal);
}

.input-wrapper.focused {
  transform: translateY(-2px);
}

.input-icon {
  position: absolute;
  left: var(--space-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-light);
  transition: var(--transition-normal);
  z-index: 2;
}

.input-wrapper.focused .input-icon {
  color: var(--primary-color);
  transform: translateY(-50%) scale(1.1);
}

.input-wrapper.error .modern-input {
  border-color: var(--primary-color);
  background: rgba(255, 107, 107, 0.05);
}

.input-wrapper.error .input-icon {
  color: var(--primary-color);
}

.modern-input {
  width: 100%;
  padding: var(--space-lg) var(--space-lg) var(--space-lg) 3.5rem;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-lg);
  font-family: var(--font-primary);
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  background: var(--background-primary);
  transition: var(--transition-normal);
  outline: none;
}

.modern-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

.modern-input::placeholder {
  color: var(--text-light);
  font-weight: var(--font-weight-regular);
}

/* Passcode Inputs */
.passcode-wrapper {
  position: relative;
}

.passcode-inputs {
  display: flex;
  gap: var(--space-md);
  justify-content: center;
  transition: var(--transition-normal);
}

.passcode-inputs.shake {
  animation: shake 0.6s ease-in-out;
}

.passcode-digit {
  width: 60px;
  height: 70px;
  text-align: center;
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-lg);
  background: var(--background-primary);
  transition: var(--transition-bounce);
  outline: none;
  font-family: var(--font-primary);
}

.passcode-digit:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  transform: translateY(-2px);
}

.passcode-digit.success {
  border-color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
  animation: successPulse 0.6s ease;
}

.passcode-digit.error {
  border-color: var(--primary-color);
  background: rgba(255, 107, 107, 0.1);
}

/* Submission Indicator */
.submission-indicator {
  display: none;
  text-align: center;
  margin: var(--space-xl) 0;
  opacity: 0;
  transition: var(--transition-normal);
}

.submission-indicator.active {
  display: block;
  opacity: 1;
  animation: fadeInUp 0.5s ease-out;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  gap: var(--space-sm);
  margin-bottom: var(--space-md);
}

.spinner-ring {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--primary-color);
  animation: spinnerPulse 1.4s infinite ease-in-out;
}

.spinner-ring:nth-child(1) { animation-delay: 0s; }
.spinner-ring:nth-child(2) { animation-delay: 0.2s; }
.spinner-ring:nth-child(3) { animation-delay: 0.4s; }

.loading-text {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
  margin: 0;
}

/* Submit Button */
.modern-submit-btn {
  width: 100%;
  padding: var(--space-lg) var(--space-xl);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  border: none;
  border-radius: var(--radius-full);
  font-family: var(--font-primary);
  font-size: 1.1rem;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: var(--transition-bounce);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-md);
  box-shadow: var(--shadow-lg);
}

.modern-submit-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

.modern-submit-btn:active {
  transform: translateY(-1px);
}

.btn-text {
  position: relative;
  z-index: 2;
}

.btn-icon {
  position: relative;
  z-index: 2;
  transition: var(--transition-normal);
}

.modern-submit-btn:hover .btn-icon {
  transform: translateX(4px);
}

.btn-ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

.btn-ripple.active {
  animation: ripple 0.6s linear;
}

/* Footer */
.login-footer {
  text-align: center;
  padding-top: var(--space-xl);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.footer-text {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  margin: 0;
}

.footer-text i {
  color: var(--accent-color);
}

/* Floating Elements */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.float-element {
  position: absolute;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  opacity: 0.1;
  animation: floatAround 12s ease-in-out infinite;
}

.element-1 {
  top: 15%;
  left: 5%;
  animation-delay: 0s;
}

.element-2 {
  top: 25%;
  right: 8%;
  animation-delay: 3s;
}

.element-3 {
  bottom: 30%;
  left: 8%;
  animation-delay: 6s;
}

.element-4 {
  bottom: 20%;
  right: 5%;
  animation-delay: 9s;
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(5deg);
  }
}

@keyframes floatAround {
  0%, 100% {
    transform: translateY(0px) translateX(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-15px) translateX(10px) rotate(5deg);
  }
  50% {
    transform: translateY(-10px) translateX(-5px) rotate(-3deg);
  }
  75% {
    transform: translateY(-20px) translateX(8px) rotate(7deg);
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-8px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(8px);
  }
}

@keyframes successPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spinnerPulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

@keyframes ripple {
  to {
    transform: scale(4);
    opacity: 0;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-wrapper {
    padding: var(--space-lg);
  }

  .login-card {
    padding: var(--space-2xl);
    max-width: 100%;
  }

  .brand-text {
    font-size: 1.75rem;
  }

  .login-title {
    font-size: 1.5rem;
  }

  .passcode-digit {
    width: 50px;
    height: 60px;
    font-size: 1.25rem;
  }

  .passcode-inputs {
    gap: var(--space-sm);
  }

  .floating-elements {
    display: none;
  }

  .shape {
    display: none;
  }
}

@media (max-width: 480px) {
  .login-card {
    padding: var(--space-xl);
    border-radius: var(--radius-xl);
  }

  .passcode-digit {
    width: 45px;
    height: 55px;
    font-size: 1.1rem;
  }

  .modern-input {
    padding: var(--space-md) var(--space-md) var(--space-md) 3rem;
  }

  .input-icon {
    left: var(--space-md);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .login-card {
    border: 2px solid var(--text-primary);
    background: var(--background-primary);
  }

  .modern-input,
  .passcode-digit {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .floating-elements,
  .bg-shapes {
    display: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #F8F9FA;
    --text-secondary: #B2BEC3;
    --text-light: #636E72;
    --background-primary: #2D3436;
    --background-secondary: #636E72;
  }

  .modern-login-body {
    background: linear-gradient(135deg, var(--background-dark) 0%, var(--background-secondary) 100%);
  }

  .login-card {
    background: rgba(45, 52, 54, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .modern-input,
  .passcode-digit {
    background: var(--background-secondary);
    border-color: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
  }

  .modern-input::placeholder {
    color: var(--text-light);
  }
}
