#!/usr/bin/env python3
"""
Test script for the data processor module
"""

from data_processor import CoreValuesDataProcessor, create_sample_data
import json

def test_basic_functionality():
    """Test basic functionality of the data processor"""
    print("Testing CoreValuesDataProcessor...")
    
    # Create processor with custom core values
    processor = CoreValuesDataProcessor(['Trust', 'Innovation', 'Teamwork'])
    
    # Add team member
    processor.add_team_member_rating(
        'test1', 'Test User', 'Developer',
        {'Trust': 4, 'Innovation': 5, 'Teamwork': 3}
    )
    
    # Add manager
    processor.set_manager_rating(
        'mgr1', 'Test Manager',
        {'Trust': 5, 'Innovation': 4, 'Teamwork': 4}
    )
    
    # Test calculations
    averages = processor.calculate_team_averages()
    print(f"Team averages: {averages}")
    
    stats = processor.calculate_team_statistics()
    print(f"Team statistics: {stats}")
    
    comparison = processor.get_manager_vs_team_comparison()
    print(f"Manager vs team: {comparison}")
    
    # Test export
    viz_data = processor.export_for_visualization()
    print(f"Visualization data keys: {list(viz_data.keys())}")
    
    print("✅ Basic functionality test passed!")

def test_sample_data():
    """Test the sample data creation"""
    print("\nTesting sample data creation...")
    
    processor = create_sample_data()
    viz_data = processor.export_for_visualization()
    
    print(f"Core values: {viz_data['core_values']}")
    print(f"Team members: {viz_data['team_members']}")
    print(f"Manager: {viz_data['manager_name']}")
    print(f"Team data shape: {len(viz_data['team_data'])}x{len(viz_data['team_data'][0]) if viz_data['team_data'] else 0}")
    print(f"Manager data: {viz_data['manager_data']}")
    print(f"Team averages: {viz_data['team_averages']}")
    
    print("✅ Sample data test passed!")

def test_database_simulation():
    """Test loading data from simulated database records"""
    print("\nTesting database record loading...")
    
    # Simulate database records
    records = [
        {'user_id': 'u1', 'name': 'John', 'role': 'team_member', 'core_value': 'Důvěra', 'rating': 4, 'timestamp': '2024-01-01T10:00:00'},
        {'user_id': 'u1', 'name': 'John', 'role': 'team_member', 'core_value': 'Odpovědnost', 'rating': 3, 'timestamp': '2024-01-01T10:00:00'},
        {'user_id': 'u1', 'name': 'John', 'role': 'team_member', 'core_value': 'Inovace', 'rating': 5, 'timestamp': '2024-01-01T10:00:00'},
        {'user_id': 'u1', 'name': 'John', 'role': 'team_member', 'core_value': 'Spolupráce', 'rating': 4, 'timestamp': '2024-01-01T10:00:00'},
        {'user_id': 'u1', 'name': 'John', 'role': 'team_member', 'core_value': 'Respekt', 'rating': 4, 'timestamp': '2024-01-01T10:00:00'},
        
        {'user_id': 'mgr1', 'name': 'Sarah', 'role': 'manager', 'core_value': 'Důvěra', 'rating': 5, 'timestamp': '2024-01-01T11:00:00'},
        {'user_id': 'mgr1', 'name': 'Sarah', 'role': 'manager', 'core_value': 'Odpovědnost', 'rating': 5, 'timestamp': '2024-01-01T11:00:00'},
        {'user_id': 'mgr1', 'name': 'Sarah', 'role': 'manager', 'core_value': 'Inovace', 'rating': 4, 'timestamp': '2024-01-01T11:00:00'},
        {'user_id': 'mgr1', 'name': 'Sarah', 'role': 'manager', 'core_value': 'Spolupráce', 'rating': 5, 'timestamp': '2024-01-01T11:00:00'},
        {'user_id': 'mgr1', 'name': 'Sarah', 'role': 'manager', 'core_value': 'Respekt', 'rating': 5, 'timestamp': '2024-01-01T11:00:00'},
    ]
    
    processor = CoreValuesDataProcessor()
    processor.load_from_database_records(records)
    
    viz_data = processor.export_for_visualization()
    print(f"Loaded {len(viz_data['team_members'])} team members")
    print(f"Manager: {viz_data['manager_name']}")
    print(f"Team member ratings: {viz_data['team_data']}")
    print(f"Manager ratings: {viz_data['manager_data']}")
    
    print("✅ Database simulation test passed!")

def test_json_export():
    """Test JSON export functionality"""
    print("\nTesting JSON export...")
    
    processor = create_sample_data()
    json_str = processor.export_to_json()
    
    # Verify it's valid JSON
    data = json.loads(json_str)
    print(f"JSON export contains {len(data)} keys")
    print(f"Core values in JSON: {data['core_values']}")
    
    print("✅ JSON export test passed!")

if __name__ == "__main__":
    try:
        test_basic_functionality()
        test_sample_data()
        test_database_simulation()
        test_json_export()
        print("\n🎉 All tests passed successfully!")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
