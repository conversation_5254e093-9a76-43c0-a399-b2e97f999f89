{"core_values": ["Důvěra", "Odpovědnost", "Inovace", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Respekt"], "team_ratings": [{"user_id": "user1", "name": "<PERSON>", "role": "Developer", "ratings": {"Důvěra": 4, "Odpovědnost": 3, "Inovace": 5, "Spolupráce": 2, "Respekt": 4}, "timestamp": "2025-05-25T22:00:21.193974"}, {"user_id": "user2", "name": "<PERSON>", "role": "Designer", "ratings": {"Důvěra": 3, "Odpovědnost": 4, "Inovace": 4, "Spolupráce": 3, "Respekt": 5}, "timestamp": "2025-05-25T22:00:21.193974"}, {"user_id": "user3", "name": "<PERSON>", "role": "Analyst", "ratings": {"Důvěra": 2, "Odpovědnost": 3, "Inovace": 3, "Spolupráce": 4, "Respekt": 3}, "timestamp": "2025-05-25T22:00:21.193974"}, {"user_id": "user4", "name": "<PERSON>", "role": "QA Engineer", "ratings": {"Důvěra": 5, "Odpovědnost": 4, "Inovace": 4, "Spolupráce": 4, "Respekt": 5}, "timestamp": "2025-05-25T22:00:21.193974"}, {"user_id": "user5", "name": "<PERSON>", "role": "DevOps", "ratings": {"Důvěra": 3, "Odpovědnost": 3, "Inovace": 4, "Spolupráce": 3, "Respekt": 4}, "timestamp": "2025-05-25T22:00:21.193974"}], "manager_rating": {"user_id": "mgr1", "name": "Manager", "ratings": {"Důvěra": 4, "Odpovědnost": 5, "Inovace": 4, "Spolupráce": 5, "Respekt": 5}, "timestamp": "2025-05-25T22:00:21.193974"}, "export_timestamp": "2025-05-25T22:00:21.266501"}