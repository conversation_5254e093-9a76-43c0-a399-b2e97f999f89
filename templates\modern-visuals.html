<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>strategy.co - Vizualizace dat</title>
  <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.svg') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='modern-style.css') }}">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body class="modern-body">
  <!-- Navigation -->
  <nav class="modern-nav" id="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <a href="/index" class="brand-link">
          <span class="brand-text">strategy.co</span>
        </a>
      </div>
      <div class="nav-menu" id="nav-menu">
        <a href="/index" class="nav-link">Domů</a>
        <a href="/index#services" class="nav-link">Služby</a>
        <a href="/index#stats" class="nav-link">Reference</a>
        <a href="/index#team" class="nav-link">Tým</a>
        <a href="/visuals" class="nav-link active">
          <i class="fas fa-chart-bar"></i>
          <span>Vizualizace</span>
        </a>
        <a href="/index#contact" class="nav-link">Kontakt</a>
        <a href="/logout" class="nav-link logout-btn">
          <i class="fas fa-sign-out-alt"></i>
          <span>Odhlásit</span>
        </a>
      </div>
      <div class="nav-toggle" id="nav-toggle">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero-fullscreen visuals-hero">
    <div class="hero-background">
      <div class="hero-overlay"></div>
    </div>
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          <span class="title-line">Vizualizace</span>
          <span class="title-line highlight">základních hodnot</span>
        </h1>
        <p class="hero-subtitle" style="color: rgba(255, 255, 255, 0.9); text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);">
          Interaktivní analýza vnímání základních hodnot organizace pomocí moderních datových vizualizací.
        </p>
      </div>
    </div>
    <div class="scroll-indicator" data-scroll="radar">
      <div class="scroll-arrow">
        <i class="fas fa-chart-radar"></i>
      </div>
    </div>
  </section>

  <!-- Radar Chart Section -->
  <section id="radar" class="visuals-section">
    <div class="container">
      <div class="section-header">
        <span class="section-tag">Radar Chart</span>
        <h2 class="section-title">Vnímání základních hodnot</h2>
        <p class="section-subtitle">
          Interaktivní radar chart zobrazující hodnocení základních hodnot organizace jednotlivými respondenty
        </p>
      </div>

      <div class="visualization-container">
        <div class="chart-controls">
          <div class="control-group">
            <label for="viewMode">Režim zobrazení:</label>
            <select id="viewMode" class="modern-select">
              <option value="all">Všichni respondenti</option>
              <option value="team">Pouze tým</option>
              <option value="manager">Pouze manažer</option>
              <option value="comparison">Porovnání tým vs manažer</option>
            </select>
          </div>
          <div class="control-group">
            <label for="animationSpeed">Rychlost animace:</label>
            <input type="range" id="animationSpeed" min="500" max="3000" value="1500" class="modern-slider">
            <small class="speed-indicator">Pomalá ← → Rychlá</small>
          </div>
        </div>

        <div class="chart-wrapper">
          <canvas id="radarChart"></canvas>
        </div>

        <div class="chart-legend">
          <div class="legend-item">
            <div class="legend-color" style="background: var(--pantone-coral);"></div>
            <span>Tým (průměr)</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: var(--pantone-blue);"></div>
            <span>Manažer</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: var(--pantone-mint);"></div>
            <span>Individuální členové</span>
          </div>
        </div>
      </div>
    </div>
    <div class="scroll-indicator" data-scroll="pozorovani">
      <div class="scroll-arrow">
        <i class="fas fa-lightbulb"></i>
      </div>
    </div>
  </section>

  <!-- Insights Section -->
  <section id="pozorovani" class="insights-section">
    <div class="container">
      <div class="section-header">
        <span class="section-tag">Pozorování</span>
        <h2 class="section-title">Klíčová zjištění</h2>
        <p class="section-subtitle">
          Analýza dat odhaluje důležité trendy ve vnímání základních hodnot
        </p>
      </div>

      <div class="insights-grid">
        <div class="insight-card" data-aos="fade-up" data-aos-delay="100">
          <div class="insight-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <h3>Nejvyšší hodnocení</h3>
          <p>Hodnota "Důvěra" dosahuje nejvyššího průměrného hodnocení napříč všemi respondenty.</p>
          <div class="insight-metric">
            <span class="metric-value">4.2</span>
            <span class="metric-label">Průměrné hodnocení</span>
          </div>
        </div>

        <div class="insight-card" data-aos="fade-up" data-aos-delay="200">
          <div class="insight-icon">
            <i class="fas fa-users"></i>
          </div>
          <h3>Konsensus týmu</h3>
          <p>Nejvyšší shoda v hodnocení je u hodnoty "Spolupráce" s nejnižší směrodatnou odchylkou.</p>
          <div class="insight-metric">
            <span class="metric-value">0.8</span>
            <span class="metric-label">Směrodatná odchylka</span>
          </div>
        </div>

        <div class="insight-card" data-aos="fade-up" data-aos-delay="300">
          <div class="insight-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <h3>Potenciál růstu</h3>
          <p>Hodnota "Inovace" má největší potenciál pro zlepšení s nejširším rozpětím hodnocení.</p>
          <div class="insight-metric">
            <span class="metric-value">3.2</span>
            <span class="metric-label">Rozpětí hodnocení</span>
          </div>
        </div>

        <div class="insight-card" data-aos="fade-up" data-aos-delay="400">
          <div class="insight-icon">
            <i class="fas fa-balance-scale"></i>
          </div>
          <h3>Manažerský pohled</h3>
          <p>Manažer hodnotí všechny hodnoty konzistentně výše než průměr týmu.</p>
          <div class="insight-metric">
            <span class="metric-value">+0.7</span>
            <span class="metric-label">Rozdíl od průměru</span>
          </div>
        </div>
      </div>
    </div>
    <div class="scroll-indicator scroll-up" data-scroll="home">
      <div class="scroll-arrow">
        <i class="fas fa-chevron-up"></i>
      </div>
    </div>
  </section>

  <script src="{{ url_for('static', filename='modern-script.js') }}"></script>
  <script>
    // Load data from server
    const vizData = {{ viz_data | safe }};

    // Extract data for radar chart
    const coreValues = vizData.core_values;
    const teamMembers = vizData.team_members;
    const teamData = vizData.team_data;
    const teamAverages = vizData.team_averages;
    const managerName = vizData.manager_name;
    const managerData = vizData.manager_data;

    // Use pre-calculated team averages from server
    function calculateTeamAverages() {
      return teamAverages;
    }

    let currentChart = null;

    function createRadarChart() {
      const ctx = document.getElementById('radarChart').getContext('2d');
      const viewMode = document.getElementById('viewMode').value;
      // Reverse the animation speed logic: higher slider value = faster (lower duration)
      const sliderValue = parseInt(document.getElementById('animationSpeed').value);
      const animationSpeed = 4000 - sliderValue; // 3500ms (slow) to 500ms (fast)

      if (currentChart) {
        currentChart.destroy();
      }

      const datasets = [];

      if (viewMode === 'all' || viewMode === 'team') {
        // Add individual team members
        if (viewMode === 'all') {
          teamData.forEach((memberData, index) => {
            datasets.push({
              label: teamMembers[index],
              data: memberData,
              borderColor: `rgba(78, 205, 196, ${0.3 + index * 0.15})`,
              backgroundColor: `rgba(78, 205, 196, ${0.1 + index * 0.05})`,
              borderWidth: 2,
              pointBackgroundColor: '#4ECDC4',
              pointBorderColor: '#ffffff',
              pointBorderWidth: 2,
              pointRadius: 4
            });
          });
        }

        // Add team average
        datasets.push({
          label: 'Průměr týmu',
          data: calculateTeamAverages(),
          borderColor: '#FF6B6B',
          backgroundColor: 'rgba(255, 107, 107, 0.2)',
          borderWidth: 3,
          pointBackgroundColor: '#FF6B6B',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 3,
          pointRadius: 6
        });
      }

      if (viewMode === 'all' || viewMode === 'manager' || viewMode === 'comparison') {
        datasets.push({
          label: managerName || 'Manažer',
          data: managerData,
          borderColor: '#0F4C75',
          backgroundColor: 'rgba(15, 76, 117, 0.2)',
          borderWidth: 3,
          pointBackgroundColor: '#0F4C75',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 3,
          pointRadius: 6
        });
      }

      if (viewMode === 'comparison') {
        // Only show team average and manager - ensure team average is added first
        datasets.length = 0;

        // Add team average
        datasets.push({
          label: 'Průměr týmu',
          data: calculateTeamAverages(),
          borderColor: '#FF6B6B',
          backgroundColor: 'rgba(255, 107, 107, 0.2)',
          borderWidth: 3,
          pointBackgroundColor: '#FF6B6B',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 3,
          pointRadius: 6
        });

        // Add manager
        datasets.push({
          label: managerName || 'Manažer',
          data: managerData,
          borderColor: '#0F4C75',
          backgroundColor: 'rgba(15, 76, 117, 0.2)',
          borderWidth: 3,
          pointBackgroundColor: '#0F4C75',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 3,
          pointRadius: 6
        });
      }

      currentChart = new Chart(ctx, {
        type: 'radar',
        data: {
          labels: coreValues,
          datasets: datasets
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          animation: {
            duration: animationSpeed
          },
          scales: {
            r: {
              beginAtZero: true,
              min: 0,
              max: 5,
              ticks: {
                stepSize: 1,
                color: '#636E72',
                font: {
                  family: 'Inter',
                  size: 12
                }
              },
              grid: {
                color: 'rgba(0, 0, 0, 0.1)'
              },
              angleLines: {
                color: 'rgba(0, 0, 0, 0.1)'
              },
              pointLabels: {
                color: '#2D3436',
                font: {
                  family: 'Inter',
                  size: 14,
                  weight: '500'
                }
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                color: '#2D3436',
                font: {
                  family: 'Inter',
                  size: 12
                },
                padding: 20,
                usePointStyle: true
              }
            },
            tooltip: {
              backgroundColor: 'rgba(45, 52, 54, 0.9)',
              titleColor: '#ffffff',
              bodyColor: '#ffffff',
              borderColor: '#FF6B6B',
              borderWidth: 1,
              cornerRadius: 8,
              callbacks: {
                label: function(context) {
                  return `${context.dataset.label}: ${context.parsed.r}/5`;
                }
              }
            }
          }
        }
      });
    }

    // Initialize chart when page loads
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(createRadarChart, 500);

      // Update chart when controls change
      document.getElementById('viewMode').addEventListener('change', createRadarChart);
      document.getElementById('animationSpeed').addEventListener('input', function() {
        if (currentChart) {
          currentChart.options.animation.duration = parseInt(this.value);
          currentChart.update();
        }
      });
    });
  </script>
</body>
</html>
