<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>strategy.co - Vizualizace dat</title>
  <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.svg') }}">
  <link rel="stylesheet" href="{{ url_for('static', filename='modern-style.css') }}">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
</head>
<body class="modern-body">
  <!-- Navigation -->
  <nav class="modern-nav" id="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <a href="/index" class="brand-link">
          <span class="brand-text">strategy.co</span>
        </a>
      </div>
      <div class="nav-menu" id="nav-menu">
        <a href="/index" class="nav-link">Domů</a>
        <a href="/index#services" class="nav-link">Služby</a>
        <a href="/index#stats" class="nav-link">Reference</a>
        <a href="/index#team" class="nav-link">Tým</a>
        <a href="/visuals" class="nav-link active">
          <i class="fas fa-chart-bar"></i>
          <span>Vizualizace</span>
        </a>
        <a href="/index#contact" class="nav-link">Kontakt</a>
        <a href="/logout" class="nav-link logout-btn">
          <i class="fas fa-sign-out-alt"></i>
          <span>Odhlásit</span>
        </a>
      </div>
      <div class="nav-toggle" id="nav-toggle">
        <span class="bar"></span>
        <span class="bar"></span>
        <span class="bar"></span>
      </div>
    </div>
  </nav>

  <!-- Hero Section -->
  <section class="hero-fullscreen visuals-hero">
    <div class="hero-background">
      <div class="hero-overlay"></div>
    </div>
    <div class="hero-content">
      <div class="hero-text">
        <h1 class="hero-title">
          <span class="title-line">Vizualizace</span>
          <span class="title-line highlight">základních hodnot</span>
        </h1>
        <p class="hero-subtitle">
          Interaktivní analýza vnímání základních hodnot organizace pomocí moderních datových vizualizací.
        </p>
      </div>
    </div>
    <div class="scroll-indicator" data-scroll="heatmap">
      <div class="scroll-arrow">
        <i class="fas fa-chevron-down"></i>
      </div>
    </div>
  </section>

  <!-- Heatmap Section -->
  <section id="heatmap" class="visuals-section">
    <div class="container">
      <div class="section-header">
        <span class="section-tag">Heatmapa</span>
        <h2 class="section-title">Vnímání základních hodnot</h2>
        <p class="section-subtitle">
          Interaktivní heatmapa zobrazující hodnocení základních hodnot organizace jednotlivými respondenty
        </p>
      </div>

      <div class="visualization-container">
        <div class="chart-controls">
          <div class="control-group">
            <label for="colorScheme">Barevné schéma:</label>
            <select id="colorScheme" class="modern-select">
              <option value="coral-blue">Coral-Blue</option>
              <option value="mint-purple">Mint-Purple</option>
              <option value="warm-cool">Warm-Cool</option>
            </select>
          </div>
          <div class="control-group">
            <label for="animationSpeed">Rychlost animace:</label>
            <input type="range" id="animationSpeed" min="500" max="3000" value="1500" class="modern-slider">
          </div>
        </div>

        <div class="chart-wrapper">
          <canvas id="heatmapChart"></canvas>
        </div>

        <div class="chart-legend">
          <div class="legend-item">
            <div class="legend-color" style="background: var(--pantone-coral);"></div>
            <span>Vysoké hodnocení (4-5)</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: var(--pantone-mint);"></div>
            <span>Střední hodnocení (3)</span>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background: var(--pantone-blue);"></div>
            <span>Nízké hodnocení (1-2)</span>
          </div>
        </div>
      </div>
    </div>
    <div class="scroll-indicator" data-scroll="insights">
      <div class="scroll-arrow">
        <i class="fas fa-chevron-down"></i>
      </div>
    </div>
  </section>

  <!-- Insights Section -->
  <section id="insights" class="insights-section">
    <div class="container">
      <div class="section-header">
        <span class="section-tag">Pozorování</span>
        <h2 class="section-title">Klíčová zjištění</h2>
        <p class="section-subtitle">
          Analýza dat odhaluje důležité trendy ve vnímání základních hodnot
        </p>
      </div>

      <div class="insights-grid">
        <div class="insight-card" data-aos="fade-up" data-aos-delay="100">
          <div class="insight-icon">
            <i class="fas fa-trophy"></i>
          </div>
          <h3>Nejvyšší hodnocení</h3>
          <p>Hodnota "Důvěra" dosahuje nejvyššího průměrného hodnocení napříč všemi respondenty.</p>
          <div class="insight-metric">
            <span class="metric-value">4.2</span>
            <span class="metric-label">Průměrné hodnocení</span>
          </div>
        </div>

        <div class="insight-card" data-aos="fade-up" data-aos-delay="200">
          <div class="insight-icon">
            <i class="fas fa-users"></i>
          </div>
          <h3>Konsensus týmu</h3>
          <p>Nejvyšší shoda v hodnocení je u hodnoty "Spolupráce" s nejnižší směrodatnou odchylkou.</p>
          <div class="insight-metric">
            <span class="metric-value">0.8</span>
            <span class="metric-label">Směrodatná odchylka</span>
          </div>
        </div>

        <div class="insight-card" data-aos="fade-up" data-aos-delay="300">
          <div class="insight-icon">
            <i class="fas fa-chart-line"></i>
          </div>
          <h3>Potenciál růstu</h3>
          <p>Hodnota "Inovace" má největší potenciál pro zlepšení s nejširším rozpětím hodnocení.</p>
          <div class="insight-metric">
            <span class="metric-value">3.2</span>
            <span class="metric-label">Rozpětí hodnocení</span>
          </div>
        </div>

        <div class="insight-card" data-aos="fade-up" data-aos-delay="400">
          <div class="insight-icon">
            <i class="fas fa-balance-scale"></i>
          </div>
          <h3>Manažerský pohled</h3>
          <p>Manažer hodnotí všechny hodnoty konzistentně výše než průměr týmu.</p>
          <div class="insight-metric">
            <span class="metric-value">+0.7</span>
            <span class="metric-label">Rozdíl od průměru</span>
          </div>
        </div>
      </div>
    </div>
    <div class="scroll-indicator scroll-up" data-scroll="heatmap">
      <div class="scroll-arrow">
        <i class="fas fa-chevron-up"></i>
      </div>
    </div>
  </section>

  <script src="{{ url_for('static', filename='modern-script.js') }}"></script>
  <script>
    // Heatmap data (equivalent to the Python data)
    const coreValues = ['Trust', 'Accountability', 'Innovation', 'Collaboration', 'Respect'];
    const users = ['Alice', 'Bob', 'Charlie', 'Dana', 'Eli', 'Manager'];
    
    const data = [
      [4, 3, 5, 2, 4], // Alice
      [3, 4, 4, 3, 5], // Bob
      [2, 3, 3, 4, 3], // Charlie
      [5, 4, 4, 4, 5], // Dana
      [3, 3, 4, 3, 4], // Eli
      [4, 5, 4, 5, 5]  // Manager
    ];

    // Color schemes
    const colorSchemes = {
      'coral-blue': {
        low: '#0F4C75',
        mid: '#4ECDC4', 
        high: '#FF6B6B'
      },
      'mint-purple': {
        low: '#6C5CE7',
        mid: '#A8E6CF',
        high: '#4ECDC4'
      },
      'warm-cool': {
        low: '#74b9ff',
        mid: '#fdcb6e',
        high: '#e17055'
      }
    };

    let currentChart = null;

    function getColor(value, scheme = 'coral-blue') {
      const colors = colorSchemes[scheme];
      if (value <= 2) return colors.low;
      if (value === 3) return colors.mid;
      return colors.high;
    }

    function createHeatmap() {
      const ctx = document.getElementById('heatmapChart').getContext('2d');
      const scheme = document.getElementById('colorScheme').value;
      
      // Prepare data for Chart.js
      const chartData = [];
      for (let i = 0; i < users.length; i++) {
        for (let j = 0; j < coreValues.length; j++) {
          chartData.push({
            x: j,
            y: i,
            v: data[i][j]
          });
        }
      }

      if (currentChart) {
        currentChart.destroy();
      }

      currentChart = new Chart(ctx, {
        type: 'scatter',
        data: {
          datasets: [{
            label: 'Core Values Rating',
            data: chartData,
            backgroundColor: function(context) {
              const value = context.parsed.v;
              return getColor(value, scheme);
            },
            borderColor: '#ffffff',
            borderWidth: 2,
            pointRadius: 25,
            pointHoverRadius: 30
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          animation: {
            duration: parseInt(document.getElementById('animationSpeed').value)
          },
          scales: {
            x: {
              type: 'linear',
              position: 'bottom',
              min: -0.5,
              max: coreValues.length - 0.5,
              ticks: {
                stepSize: 1,
                callback: function(value) {
                  return coreValues[value] || '';
                },
                color: '#2D3436',
                font: {
                  family: 'Inter',
                  size: 12,
                  weight: '500'
                }
              },
              grid: {
                display: false
              }
            },
            y: {
              type: 'linear',
              min: -0.5,
              max: users.length - 0.5,
              ticks: {
                stepSize: 1,
                callback: function(value) {
                  return users[value] || '';
                },
                color: '#2D3436',
                font: {
                  family: 'Inter',
                  size: 12,
                  weight: '500'
                }
              },
              grid: {
                display: false
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                title: function(context) {
                  const point = context[0];
                  return `${users[point.parsed.y]} - ${coreValues[point.parsed.x]}`;
                },
                label: function(context) {
                  return `Hodnocení: ${context.parsed.v}/5`;
                }
              },
              backgroundColor: 'rgba(45, 52, 54, 0.9)',
              titleColor: '#ffffff',
              bodyColor: '#ffffff',
              borderColor: '#FF6B6B',
              borderWidth: 1,
              cornerRadius: 8,
              displayColors: false
            }
          }
        }
      });
    }

    // Initialize chart when page loads
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(createHeatmap, 500);
      
      // Update chart when controls change
      document.getElementById('colorScheme').addEventListener('change', createHeatmap);
      document.getElementById('animationSpeed').addEventListener('input', function() {
        if (currentChart) {
          currentChart.options.animation.duration = parseInt(this.value);
        }
      });
    });
  </script>
</body>
</html>
